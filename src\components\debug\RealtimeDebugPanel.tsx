'use client';

import React, { useState, useEffect } from 'react';
import { realtimeDebugger } from '@/lib/utils/realtimeDebugger';
import { useSession } from 'next-auth/react';

interface DebugLog {
  timestamp: Date;
  type: string;
  message: string;
  data?: any;
}

export function RealtimeDebugPanel() {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<any>({});
  const [testConversationId, setTestConversationId] = useState('');

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Update logs every 2 seconds
      const interval = setInterval(() => {
        setLogs(realtimeDebugger.getLogs(50));
        setConnectionStatus(realtimeDebugger.getConnectionStatus());
      }, 2000);

      return () => clearInterval(interval);
    }
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const handleTestConnection = () => {
    if (testConversationId) {
      realtimeDebugger.testConnection(testConversationId);
    }
  };

  const handleTestTyping = () => {
    if (testConversationId) {
      realtimeDebugger.testTyping(testConversationId);
    }
  };

  const handleExportLogs = () => {
    realtimeDebugger.exportDebugData();
  };

  const handleClearLogs = () => {
    realtimeDebugger.clearLogs();
    setLogs([]);
  };

  return (
    <>
      {/* Debug Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        title="Real-time Debug Panel"
      >
        🐛
      </button>

      {/* Debug Panel */}
      {isOpen && (
        <div className="fixed bottom-16 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-xl w-96 max-h-96 overflow-hidden">
          <div className="bg-gray-100 px-4 py-2 border-b border-gray-300 flex justify-between items-center">
            <h3 className="font-semibold text-sm">Real-time Debug Panel</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="p-4 space-y-4">
            {/* Connection Status */}
            <div>
              <h4 className="font-medium text-xs text-gray-700 mb-2">Connection Status</h4>
              <div className="text-xs bg-gray-50 p-2 rounded">
                <div>Channels: {connectionStatus.channels || 0}</div>
                <div>Message Listeners: {connectionStatus.messageListeners || 0}</div>
                <div>Typing Listeners: {connectionStatus.typingListeners || 0}</div>
                <div>Active Timeouts: {connectionStatus.activeTimeouts || 0}</div>
              </div>
            </div>

            {/* Test Controls */}
            <div>
              <h4 className="font-medium text-xs text-gray-700 mb-2">Test Controls</h4>
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="Conversation ID (e.g., user1_user2)"
                  value={testConversationId}
                  onChange={(e) => setTestConversationId(e.target.value)}
                  className="w-full text-xs p-2 border border-gray-300 rounded"
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleTestConnection}
                    className="flex-1 text-xs bg-blue-500 text-white p-1 rounded hover:bg-blue-600"
                  >
                    Test Connection
                  </button>
                  <button
                    onClick={handleTestTyping}
                    className="flex-1 text-xs bg-green-500 text-white p-1 rounded hover:bg-green-600"
                  >
                    Test Typing
                  </button>
                </div>
              </div>
            </div>

            {/* Log Controls */}
            <div className="flex space-x-2">
              <button
                onClick={handleClearLogs}
                className="flex-1 text-xs bg-red-500 text-white p-1 rounded hover:bg-red-600"
              >
                Clear Logs
              </button>
              <button
                onClick={handleExportLogs}
                className="flex-1 text-xs bg-purple-500 text-white p-1 rounded hover:bg-purple-600"
              >
                Export
              </button>
            </div>

            {/* Recent Logs */}
            <div>
              <h4 className="font-medium text-xs text-gray-700 mb-2">Recent Logs</h4>
              <div className="max-h-32 overflow-y-auto text-xs bg-gray-50 p-2 rounded">
                {logs.length === 0 ? (
                  <div className="text-gray-500">No logs yet...</div>
                ) : (
                  logs.slice(-10).map((log, index) => (
                    <div key={index} className="mb-1 border-b border-gray-200 pb-1">
                      <div className="flex justify-between">
                        <span className="font-medium text-blue-600">{log.type}</span>
                        <span className="text-gray-500">
                          {log.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="text-gray-700">{log.message}</div>
                      {log.data && (
                        <div className="text-gray-500 text-xs">
                          {JSON.stringify(log.data, null, 2).substring(0, 100)}...
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
