/**
 * Real-time Debugging Utilities
 * Helps debug and monitor real-time messaging and typing indicators
 */

import { realtimeMessageService } from '../services/realtimeMessageService';

export class RealtimeDebugger {
  private static instance: RealtimeDebugger;
  private debugMode = false;
  private logs: Array<{ timestamp: Date; type: string; message: string; data?: any }> = [];

  static getInstance(): RealtimeDebugger {
    if (!RealtimeDebugger.instance) {
      RealtimeDebugger.instance = new RealtimeDebugger();
    }
    return RealtimeDebugger.instance;
  }

  /**
   * Enable debug mode
   */
  enableDebug() {
    this.debugMode = true;
    console.log('🐛 Real-time debugging enabled');
    
    // Add to window for browser console access
    if (typeof window !== 'undefined') {
      (window as any).realtimeDebugger = this;
    }
  }

  /**
   * Disable debug mode
   */
  disableDebug() {
    this.debugMode = false;
    console.log('🐛 Real-time debugging disabled');
  }

  /**
   * Log debug information
   */
  log(type: string, message: string, data?: any) {
    const logEntry = {
      timestamp: new Date(),
      type,
      message,
      data
    };

    this.logs.push(logEntry);

    // Keep only last 100 logs
    if (this.logs.length > 100) {
      this.logs.shift();
    }

    if (this.debugMode) {
      console.log(`🐛 [${type}] ${message}`, data || '');
    }
  }

  /**
   * Get recent logs
   */
  getLogs(count = 20) {
    return this.logs.slice(-count);
  }

  /**
   * Clear logs
   */
  clearLogs() {
    this.logs = [];
    console.log('🐛 Debug logs cleared');
  }

  /**
   * Test real-time connection
   */
  async testConnection(conversationId: string) {
    this.log('TEST', `Testing connection for conversation: ${conversationId}`);
    
    try {
      // Test message subscription
      realtimeMessageService.subscribeToConversation(conversationId, (event) => {
        this.log('TEST_MESSAGE', `Received test message event`, event);
      });

      // Test typing subscription
      realtimeMessageService.subscribeToTyping(conversationId, (typing) => {
        this.log('TEST_TYPING', `Received typing indicator`, typing);
      });

      this.log('TEST', 'Connection test initiated successfully');
    } catch (error) {
      this.log('TEST_ERROR', 'Connection test failed', error);
    }
  }

  /**
   * Test typing indicator
   */
  async testTyping(conversationId: string) {
    this.log('TEST', `Testing typing indicator for conversation: ${conversationId}`);
    
    try {
      await realtimeMessageService.startTyping(conversationId);
      this.log('TEST_TYPING', 'Started typing indicator');
      
      setTimeout(async () => {
        await realtimeMessageService.stopTyping(conversationId);
        this.log('TEST_TYPING', 'Stopped typing indicator');
      }, 3000);
    } catch (error) {
      this.log('TEST_ERROR', 'Typing test failed', error);
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    const status = {
      channels: (realtimeMessageService as any).channels.size,
      messageListeners: (realtimeMessageService as any).messageListeners.size,
      typingListeners: (realtimeMessageService as any).typingListeners.size,
      activeTimeouts: (realtimeMessageService as any).typingTimeouts.size,
    };

    this.log('STATUS', 'Connection status retrieved', status);
    return status;
  }

  /**
   * Monitor real-time performance
   */
  startPerformanceMonitoring() {
    this.log('MONITOR', 'Starting performance monitoring');
    
    setInterval(() => {
      const status = this.getConnectionStatus();
      this.log('PERFORMANCE', 'Performance check', {
        ...status,
        memoryUsage: typeof window !== 'undefined' ? (performance as any).memory : null,
        timestamp: new Date().toISOString()
      });
    }, 30000); // Every 30 seconds
  }

  /**
   * Export debug data
   */
  exportDebugData() {
    const debugData = {
      logs: this.logs,
      connectionStatus: this.getConnectionStatus(),
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'Server',
    };

    if (typeof window !== 'undefined') {
      const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `realtime-debug-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }

    return debugData;
  }
}

// Export singleton instance
export const realtimeDebugger = RealtimeDebugger.getInstance();

// Auto-enable in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  realtimeDebugger.enableDebug();
  realtimeDebugger.startPerformanceMonitoring();
}
