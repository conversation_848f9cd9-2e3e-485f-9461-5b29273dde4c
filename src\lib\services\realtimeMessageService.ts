/**
 * Real-time Message Service using Supabase
 * Replaces Socket.IO functionality for real-time messaging
 */

import { getSupabaseClient, getSupabaseServiceClient, MessageRealtime } from '../supabase/client';
import { realtimeHealthMonitor } from '@/lib/utils/realtimeHealthMonitor';
import { realtimeFallback } from '@/lib/utils/realtimeFallback';
import { realtimeHealthChecker, disableRealtimeFeatures } from '../utils/realtimeHealthCheck';
import { realtimeDebugger } from '@/lib/utils/realtimeDebugger';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface MessageEvent {
  type: 'new_message' | 'message_read' | 'typing_start' | 'typing_stop';
  data: any;
  userId?: string;
  conversationId?: string;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  conversationId: string;
  isTyping: boolean;
}

class RealtimeMessageService {
  private supabase = getSupabaseClient();
  private serviceSupabase = getSupabaseServiceClient();
  private channels: Map<string, RealtimeChannel> = new Map();
  private messageListeners: Map<string, (event: MessageEvent) => void> = new Map();
  private typingListeners: Map<string, (typing: TypingIndicator[]) => void> = new Map();
  private currentUserId: string | null = null;
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private connectionRetries: Map<string, number> = new Map();
  private maxRetries = 3;
  private retryDelay = 2000;

  /**
   * Initialize the service with current user
   */
  initialize(userId: string) {
    this.currentUserId = userId;
    console.log(`🚀 Realtime message service initialized for user: ${userId}`);
    this.updateUserPresence('online');

    // Set up presence heartbeat
    setInterval(() => {
      if (this.currentUserId) {
        this.updateUserPresence('online');
      }
    }, 30000); // Update every 30 seconds

    // Set up connection health monitoring
    this.setupConnectionHealthMonitoring();
  }

  /**
   * Set up connection health monitoring
   */
  private setupConnectionHealthMonitoring() {
    setInterval(() => {
      this.channels.forEach((channel, conversationId) => {
        if (channel.state === 'closed' || channel.state === 'errored') {
          console.warn(`🔄 Detected unhealthy connection for ${conversationId}, state: ${channel.state}`);

          // Get the message listener for this conversation
          const messageListener = this.messageListeners.get(conversationId);
          if (messageListener) {
            console.log(`🔄 Attempting to reconnect to ${conversationId}`);
            // Reconnect after a short delay
            setTimeout(() => {
              this.subscribeToConversation(conversationId, messageListener);
            }, 2000);
          }
        }
      });
    }, 15000); // Check every 15 seconds
  }

  /**
   * Subscribe to messages for a specific conversation with retry logic
   */
  async subscribeToConversation(conversationId: string, onMessage: (event: MessageEvent) => void) {
    if (!this.currentUserId) {
      console.error('User not initialized');
      return;
    }

    // Check if realtime is healthy before attempting subscription
    const healthCheck = await realtimeHealthChecker.checkHealth();
    if (!healthCheck.canUseRealtime) {
      console.warn('Skipping message subscription due to realtime health issues:', healthCheck.error);
      // Start fallback polling instead
      realtimeFallback.startMessagePolling(conversationId, (messages) => {
        messages.forEach(message => {
          onMessage({
            type: 'new_message',
            data: message,
            conversationId,
          });
        });
      });
      return;
    }

    this.attemptSubscription(conversationId, onMessage, 0);
  }

  /**
   * Attempt subscription with retry logic
   */
  private attemptSubscription(conversationId: string, onMessage: (event: MessageEvent) => void, retryCount: number) {
    const channelName = `conversation:${conversationId}:${Date.now()}`;

    // Remove existing subscription if any
    this.unsubscribeFromConversation(conversationId);

    try {
      // Extract user IDs from conversation ID format: userId1_userId2
      const [userId1, userId2] = conversationId.split('_');
      const otherUserId = userId1 === this.currentUserId ? userId2 : userId1;

      const channel = this.supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages_realtime',
            filter: `or(and(sender_id.eq.${this.currentUserId},receiver_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},receiver_id.eq.${this.currentUserId}))`,
          },
          (payload) => {
            console.log('📨 New message received:', payload);
            realtimeDebugger.log('MESSAGE_RECEIVED', 'New message received via Supabase', payload);

            const message = payload.new as MessageRealtime;

            // Transform Supabase message to match expected format
            const transformedMessage = {
              id: message.mysql_id,
              senderId: message.sender_id,
              receiverId: message.receiver_id,
              content: message.content,
              createdAt: message.created_at,
              read: false,
              sender: {
                id: message.sender_id,
                name: 'User', // Will be populated by client
                image: null
              }
            };

            realtimeDebugger.log('MESSAGE_TRANSFORMED', 'Message transformed for client', transformedMessage);

            onMessage({
              type: 'new_message',
              data: transformedMessage,
              conversationId,
              userId: message.sender_id,
            });
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'messages_realtime',
            filter: `or(and(sender_id.eq.${this.currentUserId},receiver_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},receiver_id.eq.${this.currentUserId}))`,
          },
          (payload) => {
            const message = payload.new as MessageRealtime;
            if (message.read_at) {
              onMessage({
                type: 'message_read',
                data: message,
                conversationId,
                userId: message.sender_id,
              });
            }
          }
        )
        .subscribe((status) => {
          console.log(`📡 Message subscription status for ${conversationId}:`, status);

          if (status === 'SUBSCRIBED') {
            console.log(`✅ Successfully subscribed to conversation: ${conversationId}`);
            this.connectionRetries.set(conversationId, 0);
            realtimeHealthMonitor.startMonitoring(`conversation:${conversationId}`);
            realtimeHealthMonitor.updateStatus(`conversation:${conversationId}`, 'connected');
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.warn(`⚠️ Subscription failed for ${conversationId}, status:`, status);
            realtimeHealthMonitor.updateStatus(`conversation:${conversationId}`, 'error', `Subscription failed: ${status}`);

            if (retryCount < this.maxRetries) {
              const nextRetry = retryCount + 1;
              console.log(`🔄 Retrying subscription for ${conversationId} (${nextRetry}/${this.maxRetries})`);

              setTimeout(() => {
                this.attemptSubscription(conversationId, onMessage, nextRetry);
              }, this.retryDelay * nextRetry);
            } else {
              console.error(`❌ Max retries reached for conversation: ${conversationId}`);
              realtimeHealthMonitor.updateStatus(`conversation:${conversationId}`, 'disconnected');

              // Start fallback polling
              console.log('🔄 Starting fallback polling for conversation:', conversationId);
              realtimeFallback.startMessagePolling(conversationId, (messages) => {
                messages.forEach(message => {
                  onMessage({
                    type: 'new_message',
                    data: message,
                    conversationId,
                  });
                });
              });
            }
          }
        });

      this.channels.set(conversationId, channel);
      this.messageListeners.set(conversationId, onMessage);
      this.connectionRetries.set(conversationId, retryCount);

    } catch (error) {
      console.error(`❌ Error setting up message subscription for ${conversationId}:`, error);

      // If it's a transport constructor error, disable realtime features
      if (error instanceof Error && error.message.includes('transport is not a constructor')) {
        console.error('❌ Supabase realtime transport error detected. Disabling realtime messages.');
        disableRealtimeFeatures();

        // Start fallback polling immediately
        realtimeFallback.startMessagePolling(conversationId, (messages) => {
          messages.forEach(message => {
            onMessage({
              type: 'new_message',
              data: message,
              conversationId,
            });
          });
        });
        return;
      }

      if (retryCount < this.maxRetries) {
        const nextRetry = retryCount + 1;
        console.log(`🔄 Retrying message subscription due to error (${nextRetry}/${this.maxRetries})`);

        setTimeout(() => {
          this.attemptSubscription(conversationId, onMessage, nextRetry);
        }, this.retryDelay * nextRetry);
      } else {
        console.error(`❌ Max retries reached for message subscription due to errors`);
        // Start fallback polling
        realtimeFallback.startMessagePolling(conversationId, (messages) => {
          messages.forEach(message => {
            onMessage({
              type: 'new_message',
              data: message,
              conversationId,
            });
          });
        });
      }
    }
  }

  /**
   * Subscribe to typing indicators for a conversation
   */
  subscribeToTyping(conversationId: string, onTyping: (typing: TypingIndicator[]) => void) {
    if (!this.currentUserId) return;

    const channelName = `typing:${conversationId}:${Date.now()}`;

    const channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
          filter: `typing_in=eq.${conversationId}`,
        },
        async (payload) => {
          console.log('👀 Typing indicator change:', payload);

          // Fetch current typing users with debouncing
          setTimeout(async () => {
            const typingUsers = await this.getTypingUsers(conversationId);
            onTyping(typingUsers);
          }, 100); // Small delay to batch rapid changes
        }
      )
      .subscribe((status) => {
        console.log(`👀 Typing subscription status for ${conversationId}:`, status);
      });

    this.channels.set(`typing:${conversationId}`, channel);
    this.typingListeners.set(conversationId, onTyping);
  }

  /**
   * Send a real-time message
   */
  async sendRealtimeMessage(
    mysqlMessageId: string,
    senderId: string,
    receiverId: string,
    content: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.serviceSupabase
        .from('messages_realtime')
        .insert({
          mysql_id: mysqlMessageId,
          sender_id: senderId,
          receiver_id: receiverId,
          content,
        });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(mysqlMessageId: string): Promise<void> {
    try {
      // Note: Current schema doesn't have read_at column
      // This would need to be implemented based on actual schema
      console.log('Message read status update requested for:', mysqlMessageId);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  /**
   * Start typing indicator with debouncing
   */
  async startTyping(conversationId: string): Promise<void> {
    if (!this.currentUserId) return;

    const timeoutKey = `${this.currentUserId}:${conversationId}`;

    // Clear existing timeout
    if (this.typingTimeouts.has(timeoutKey)) {
      clearTimeout(this.typingTimeouts.get(timeoutKey)!);
    }

    try {
      // Update presence and typing status in a single operation
      await this.serviceSupabase.rpc('update_user_presence', {
        p_user_id: this.currentUserId,
        p_status: 'online',
      });

      await this.serviceSupabase
        .from('user_presence')
        .upsert({
          user_id: this.currentUserId,
          typing_in: conversationId,
          updated_at: new Date().toISOString(),
          status: 'online'
        }, {
          onConflict: 'user_id'
        });

      // Auto-stop typing after 2 seconds of inactivity
      const timeout = setTimeout(() => {
        this.stopTyping(conversationId);
      }, 2000);

      this.typingTimeouts.set(timeoutKey, timeout);
    } catch (error) {
      console.error('Error starting typing:', error);
    }
  }

  /**
   * Stop typing indicator
   */
  async stopTyping(conversationId: string): Promise<void> {
    if (!this.currentUserId) return;

    const timeoutKey = `${this.currentUserId}:${conversationId}`;

    // Clear timeout
    if (this.typingTimeouts.has(timeoutKey)) {
      clearTimeout(this.typingTimeouts.get(timeoutKey)!);
      this.typingTimeouts.delete(timeoutKey);
    }

    try {
      await this.serviceSupabase
        .from('user_presence')
        .update({
          typing_in: null,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', this.currentUserId);
    } catch (error) {
      console.error('Error stopping typing:', error);
    }
  }

  /**
   * Update user presence status
   */
  async updateUserPresence(status: 'online' | 'offline' | 'away'): Promise<void> {
    if (!this.currentUserId) return;

    try {
      await this.serviceSupabase.rpc('update_user_presence', {
        p_user_id: this.currentUserId,
        p_status: status,
      });
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  }

  /**
   * Get typing users for a conversation
   */
  private async getTypingUsers(conversationId: string): Promise<TypingIndicator[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_presence')
        .select('user_id, updated_at, status')
        .eq('typing_in', conversationId)
        .not('typing_in', 'is', null)
        .neq('user_id', this.currentUserId) // Exclude current user
        .gte('updated_at', new Date(Date.now() - 3000).toISOString()); // Last 3 seconds

      if (error || !data) {
        console.log('No typing users found or error:', error);
        return [];
      }

      console.log('👀 Found typing users:', data);

      return data
        .filter(user => user.status === 'online') // Only online users
        .map((user) => ({
          userId: user.user_id,
          userName: user.user_id, // We'll need to fetch this from MySQL if needed
          conversationId,
          isTyping: true,
        }));
    } catch (error) {
      console.error('Error fetching typing users:', error);
      return [];
    }
  }

  /**
   * Unsubscribe from conversation
   */
  unsubscribeFromConversation(conversationId: string) {
    const channel = this.channels.get(conversationId);
    if (channel) {
      this.supabase.removeChannel(channel);
      this.channels.delete(conversationId);
    }

    const typingChannel = this.channels.get(`typing:${conversationId}`);
    if (typingChannel) {
      this.supabase.removeChannel(typingChannel);
      this.channels.delete(`typing:${conversationId}`);
    }

    this.messageListeners.delete(conversationId);
    this.typingListeners.delete(conversationId);
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup() {
    console.log('🧹 Cleaning up realtime message service');

    // Stop all typing indicators first
    this.typingTimeouts.forEach((timeout) => clearTimeout(timeout));
    this.typingTimeouts.clear();

    // Remove all channels
    this.channels.forEach((channel, channelId) => {
      try {
        this.supabase.removeChannel(channel);
        realtimeHealthMonitor.stopMonitoring(`conversation:${channelId}`);
      } catch (error) {
        console.warn(`Error removing channel ${channelId}:`, error);
      }
    });

    this.channels.clear();
    this.messageListeners.clear();
    this.typingListeners.clear();
    this.connectionRetries.clear();

    // Update user presence to offline
    if (this.currentUserId) {
      this.updateUserPresence('offline');
      this.currentUserId = null;
    }
  }
}

// Export singleton instance
export const realtimeMessageService = new RealtimeMessageService();
