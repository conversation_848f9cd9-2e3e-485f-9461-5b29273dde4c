# রিয়েল-টাইম মেসেজিং সিস্টেম টেস্টিং গাইড

## সমস্যা সমাধান

### মূল সমস্যাগুলি যা ফিক্স করা হয়েছে:

1. **মেসেজ রিয়েল-টাইমে দেখাচ্ছে না**
   - Supabase ফিল্টার সিস্টেম উন্নত করা হয়েছে
   - দুই দিকের মেসেজ সাবস্ক্রিপশন যোগ করা হয়েছে
   - মেসেজ ট্রান্সফরমেশন উন্নত করা হয়েছে

2. **টাইপিং ইন্ডিকেটর কার্যকর নয়**
   - ডিবাউন্সিং যোগ করা হয়েছে
   - টাইমআউট সিস্টেম উন্নত করা হয়েছে
   - রিয়েল-টাইম আপডেট অপ্টিমাইজ করা হয়েছে

3. **কানেকশন স্থিতিশীলতা**
   - অটো-রিকানেক্ট সিস্টেম যোগ করা হয়েছে
   - হেলথ মনিটরিং উন্নত করা হয়েছে
   - ফলব্যাক সিস্টেম উন্নত করা হয়েছে

## টেস্টিং নির্দেশনা

### 1. ডেভেলপমেন্ট মোডে টেস্ট করুন

```bash
npm run dev
```

### 2. ডিবাগ প্যানেল ব্যবহার করুন

- ডেভেলপমেন্ট মোডে, পেজের নিচে ডানদিকে একটি 🐛 বাটন দেখতে পাবেন
- এটি ক্লিক করে ডিবাগ প্যানেল খুলুন
- রিয়েল-টাইম কানেকশন স্ট্যাটাস দেখুন

### 3. রিয়েল-টাইম মেসেজিং টেস্ট

#### দুটি ব্রাউজার ট্যাব খুলুন:
1. প্রথম ট্যাবে User A দিয়ে লগইন করুন
2. দ্বিতীয় ট্যাবে User B দিয়ে লগইন করুন

#### মেসেজ টেস্ট:
1. User A থেকে User B কে মেসেজ পাঠান
2. User B এর ট্যাবে তাৎক্ষণিক মেসেজ দেখা উচিত
3. রিফ্রেশ ছাড়াই মেসেজ আসা উচিত

#### টাইপিং ইন্ডিকেটর টেস্ট:
1. User A মেসেজ বক্সে টাইপ করা শুরু করুন
2. User B এর ট্যাবে "Typing..." ইন্ডিকেটর দেখা উচিত
3. টাইপ করা বন্ধ করলে ইন্ডিকেটর অদৃশ্য হওয়া উচিত

### 4. ডিবাগ কমান্ড

ব্রাউজার কনসোলে এই কমান্ডগুলি ব্যবহার করুন:

```javascript
// ডিবাগ মোড চালু করুন
realtimeDebugger.enableDebug()

// কানেকশন স্ট্যাটাস দেখুন
realtimeDebugger.getConnectionStatus()

// সাম্প্রতিক লগ দেখুন
realtimeDebugger.getLogs(20)

// কানেকশন টেস্ট করুন
realtimeDebugger.testConnection('user1_user2')

// টাইপিং টেস্ট করুন
realtimeDebugger.testTyping('user1_user2')

// ডিবাগ ডেটা এক্সপোর্ট করুন
realtimeDebugger.exportDebugData()
```

### 5. সমস্যা সমাধান

#### যদি মেসেজ রিয়েল-টাইমে না আসে:

1. ব্রাউজার কনসোল চেক করুন
2. Supabase কানেকশন স্ট্যাটাস দেখুন
3. ডিবাগ প্যানেলে কানেকশন স্ট্যাটাস চেক করুন
4. নেটওয়ার্ক ট্যাবে WebSocket কানেকশন দেখুন

#### যদি টাইপিং ইন্ডিকেটর কাজ না করে:

1. user_presence টেবিল চেক করুন
2. টাইপিং সাবস্ক্রিপশন স্ট্যাটাস দেখুন
3. ডিবাগ লগে টাইপিং ইভেন্ট চেক করুন

### 6. পারফরম্যান্স মনিটরিং

- ডিবাগ প্যানেল প্রতি 30 সেকেন্ডে পারফরম্যান্স ডেটা সংগ্রহ করে
- মেমোরি ব্যবহার এবং কানেকশন স্ট্যাটাস ট্র্যাক করে
- সমস্যা হলে অটোমেটিক লগ তৈরি করে

## প্রত্যাশিত ফলাফল

✅ **সফল টেস্ট:**
- মেসেজ তাৎক্ষণিক দেখা যাবে
- টাইপিং ইন্ডিকেটর স্মুথভাবে কাজ করবে
- কানেকশন স্থিতিশীল থাকবে
- কোনো রিফ্রেশ প্রয়োজন হবে না

❌ **সমস্যার লক্ষণ:**
- মেসেজ দেরিতে আসা
- টাইপিং ইন্ডিকেটর না দেখা
- কানেকশন ড্রপ হওয়া
- কনসোলে এরর মেসেজ

## সাপোর্ট

সমস্যা হলে:
1. ডিবাগ ডেটা এক্সপোর্ট করুন
2. ব্রাউজার কনসোল লগ সংগ্রহ করুন
3. নেটওয়ার্ক ট্যাব স্ক্রিনশট নিন
4. Supabase ড্যাশবোর্ড চেক করুন
